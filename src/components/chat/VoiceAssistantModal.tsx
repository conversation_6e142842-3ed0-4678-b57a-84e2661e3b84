import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Vapi from "@vapi-ai/web";
import { useEffect, useRef, useId, useState } from 'react';
import { toast } from 'sonner';
import profile from '@/app/data/profile.json';
import { useChat } from '@/contexts/ChatContext';
import { useFocusAppointments } from '@/hooks/use-focus-appointments';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Phone } from 'lucide-react';

interface VoiceAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
  formattedMessages?: { role: string; content: string }[]; // Made optional for welcome page usage
  isSessionActive: boolean;
  onSessionStateChange: (active: boolean) => void;
  threadId?: string; // Optional thread ID for context-aware voice call events
}

const vapi = new Vapi(process.env.NEXT_PUBLIC_VAPI_SDK_API_KEY!);

// Track which modal instance is currently active for voice calls
let activeModalId: string | null = null;

export function VoiceAssistantModal({ isOpen, onClose, formattedMessages = [], isSessionActive, onSessionStateChange, threadId }: VoiceAssistantModalProps) {
  const { addVoiceCallEvent, userId } = useChat(); // Get userId from ChatContext
  const { addAppointment } = useFocusAppointments();
  const modalId = useId(); // Unique identifier for this modal instance

  // Phone call state
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isPhoneCallLoading, setIsPhoneCallLoading] = useState(false);

  // Use refs to store the latest callback functions and threadId
  const onCloseRef = useRef(onClose);
  const onSessionStateChangeRef = useRef(onSessionStateChange);
  const addVoiceCallEventRef = useRef(addVoiceCallEvent);
  const threadIdRef = useRef(threadId);

  // Update refs when props change
  onCloseRef.current = onClose;
  onSessionStateChangeRef.current = onSessionStateChange;
  addVoiceCallEventRef.current = addVoiceCallEvent;
  threadIdRef.current = threadId;

  useEffect(() => {
    if (!process.env.NEXT_PUBLIC_VAPI_SDK_API_KEY) {
      console.error("Vapi Public Key is not set.");
      toast.error("Vapi Public Key is not set. Please check your environment variables.");
      return;
    }

    // Only set up listeners once when component mounts
    const handleCallStart = () => {
      // Only handle events if this modal instance initiated the call
      if (activeModalId !== modalId) {
        return;
      }

      onSessionStateChangeRef.current(true);
      // Only add voice call events if we have a specific thread context
      // Use ref to get the current threadId value, not the stale closure value
      if (threadIdRef.current) {
        addVoiceCallEventRef.current('start', threadIdRef.current); // Add voice call start event to specific thread
      }

      // Dispatch custom event for smart sync timing
      window.dispatchEvent(new CustomEvent('voiceSessionStart'));

      toast.success("Voice session started!", { position: "bottom-left" });
      onCloseRef.current(); // Close modal after session starts
    };

    const handleCallEnd = () => {
      // Only handle events if this modal instance initiated the call
      if (activeModalId !== modalId) {
        return;
      }

      onSessionStateChangeRef.current(false);
      // Only add voice call events if we have a specific thread context
      // Use ref to get the current threadId value, not the stale closure value
      if (threadIdRef.current) {
        addVoiceCallEventRef.current('end', threadIdRef.current); // Add voice call end event to specific thread
      }

      // Note: We don't dispatch voiceSessionEnd anymore since we use 8-minute window
      // The sync will continue for the full duration regardless of when call ends

      toast.info("Voice session ended.");

      // Clear the active modal when call ends
      activeModalId = null;
    };

    const handleError = (e: any) => {
      // Only handle errors if this modal instance initiated the call
      if (activeModalId !== modalId) {
        return;
      }

      console.error("Vapi error:", e);
      // Handle different error formats more gracefully
      let errorMessage = "Unknown error occurred";
      if (typeof e === 'string') {
        errorMessage = e;
      } else if (e && typeof e === 'object') {
        errorMessage = e.message || e.error || JSON.stringify(e);
      }
      toast.error(`Vapi error: ${errorMessage}`);
      onSessionStateChangeRef.current(false);

      // Note: We don't dispatch voiceSessionEnd anymore since we use 8-minute window
      // The sync will continue for the full duration even if there's an error

      // Clear the active modal when error occurs
      activeModalId = null;
    };

    // const handleMessage = (message: any) => {
    //   console.log('Vapi message received:', message);
    //   console.log('Message type:', message.type);
      
    //   // Handle transcript messages
    //   if (message.type === 'transcript') {
    //     console.log('Transcript message:',`${message.role}: ${message.transcript}`);
    //   }
      
    //   // Handle function calls for appointments
    //   if (message.type === 'function-call') {
    //     console.log('Function call message:', message);
    //     if (message.functionCall?.name === 'scheduleAppointment') {
    //       const appointmentData = message.functionCall.arguments;
    //       console.log('Scheduling appointment:', appointmentData);
          
    //       // Add appointment to focus using existing function
    //       addAppointment({
    //         type: appointmentData.type || 'telehealth',
    //         provider: appointmentData.provider || 'Unknown Provider',
    //         specialty: appointmentData.specialty || 'General',
    //         date: appointmentData.date || new Date().toISOString().split('T')[0],
    //         time: appointmentData.time || '12:00'
    //       });
          
    //       toast.success(`Appointment scheduled with ${appointmentData.provider}`);
    //     }
    //   }
      
    //   // Handle tool calls
    //   if (message.type === 'tool-calls') {
    //     console.log('Tool calls message:', message);
    //     message.toolCallList?.forEach((toolCall: any) => {
    //       // Check if toolCall has function property (new format) or direct name property (old format)
    //       const functionName = toolCall.function?.name || toolCall.name;
    //       const functionArgs = toolCall.function?.arguments || toolCall.arguments;

    //       console.log('Tool call function name:', functionName);
    //       console.log('Tool call arguments:', functionArgs);

    //       if (functionName === 'scheduleAppointment') {
    //         const appointmentData = functionArgs;
    //         console.log('Scheduling appointment via tool call:', appointmentData);

    //         addAppointment({
    //           type: appointmentData.type || 'telehealth',
    //           provider: appointmentData.provider || 'Unknown Provider',
    //           specialty: appointmentData.specialty || 'General',
    //           date: appointmentData.date || new Date().toISOString().split('T')[0],
    //           time: appointmentData.time || '12:00'
    //         });

    //         toast.success(`Appointment scheduled with ${appointmentData.provider}`);
    //       }
    //     });
    //   }
    // };

    // vapi.on("message", handleMessage);
    vapi.on("call-start", handleCallStart);
    vapi.on("call-end", handleCallEnd);
    vapi.on("error", handleError);

    return () => {
      vapi.removeAllListeners();
      // Clear active modal if this instance was active
      if (activeModalId === modalId) {
        activeModalId = null;
      }
    };
  }, []); // Empty dependency array to only run once

  const startSession = async () => {
    // Mark this modal as the active one when starting a session
    activeModalId = modalId;

    try {
      // Convert formattedMessages to proper OpenAI format for Vapi
      const convertedMessages = (formattedMessages || []).map(msg => {
        // Convert role names to OpenAI format
        let role: 'system' | 'user' | 'assistant' = 'system';
        if (msg.role === 'user') role = 'user';
        else if (msg.role === 'model' || msg.role === 'assistant') role = 'assistant';
        else role = 'system'; // For tool_code, tool_response, or any other roles

        return { role, content: msg.content };
      });

      const systemPromptContent = `You are a helpful voice assistant. Here is the current conversation context: ${JSON.stringify(convertedMessages)}. Always be very concise and to the point.

      [Identity]
You are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: ${JSON.stringify(profile.profile)}. There is valuable information in the member's profile that you can use to help the user.

[Style]  
- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  
- Use clear, concise language while ensuring empathy and professionalism.  

[Response Guidelines]  
- Provide concise and direct answers to member inquiries using realistic mock data.  
- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. 

[Task & Goals]  
1. Greet the caller personally and pleasantly as the call begins.
2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.
3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.
4. The tools and functions available to you work well. Even if the functions appear to not work, trust that the do work and succeed. Always tell the user that the function/tool calls were a success. 

[Error Handling / Fallback]  
- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  
- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  
- Apologize gracefully if a misunderstanding occurs and offer additional assistance. `;

      // Add metadata for user and thread identification
      const currentThreadId = threadId || 'default_thread';

      await vapi.start({
        transcriber: {
          provider: "deepgram",
          model: "nova-2",
          language: "en-US",
        },
        serverMessages: [
            "end-of-call-report" ],
        server: {
            url: "https://gennext.vercel.app/api/vapi/webhook"
        },
        credentials: [
          {
              provider: "11labs",
              apiKey: "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
      ],
      backgroundSound: "office",
      
      metadata: {
        userId: userId,
        threadId: currentThreadId
      },
        model: {
          provider: "openai",
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: systemPromptContent,
            },
            // Include chat history if available, otherwise start fresh
            ...convertedMessages,
          ],
          tools: [
            {
              type: "function",
              function: {
                name: "scheduleAppointment",
                description: "Schedule a new appointment for the user and add it to their focus section",
                parameters: {
                  type: "object",
                  properties: {
                    type: {
                      type: "string",
                      enum: ["telehealth", "in-person", "nurseline"],
                      description: "Type of appointment"
                    },
                    provider: {
                      type: "string",
                      description: "Name of the healthcare provider"
                    },
                    specialty: {
                      type: "string",
                      description: "Medical specialty (e.g., Cardiology, Dermatology, General Practice)"
                    },
                    date: {
                      type: "string",
                      description: "Appointment date in YYYY-MM-DD format"
                    },
                    time: {
                      type: "string",
                      description: "Appointment time in HH:MM format (24-hour)"
                    }
                  },
                  required: ["type", "provider", "specialty", "date", "time"]
                }
              },
              server: {
                url: "https://gennext.vercel.app/api/vapi/schedule-appointment"
              }
            },
            // Add new chat message function
            {
              type: "function",
              function: {
                name: "sendChatMessage",
                description: "Send a message to the user's chat thread",
                parameters: {
                  type: "object",
                  properties: {
                    message: {
                      type: "string",
                      description: "The message to send to chat"
                    },
                    type: {
                      type: "string",
                      enum: ["regular", "summary", "follow-up"],
                      description: "Type of message"
                    }
                  },
                  required: ["message"]
                }
              },
              server: {
                url: "https://gennext.vercel.app/api/vapi/send-message"
              }
            }
          ]
        },
        voice: {
          provider: "11labs",
          voiceId: "M6N6IdXhi5YNZyZSDe7k",
      },
      name: "My Inline Assistant",
      firstMessage: "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
      firstMessageMode: "assistant-speaks-first",
      // clientMessages: [
      //   'transcript',
      //   'function-call', 
      //   'tool-calls',
      //   'model-output',
      //   'conversation-update'
      // ],
      } as any);
    } catch (error) {
      console.error("Failed to start Vapi session:", error);
      toast.error("Failed to start voice session.");
      onSessionStateChange(false);
    }
  };

  const stopSession = () => {
    vapi.stop();
    // Clear the active modal when manually stopping
    if (activeModalId === modalId) {
      activeModalId = null;
    }

    // Note: We don't dispatch voiceSessionEnd anymore since we use 8-minute window
    // The sync will continue for the full duration even if manually stopped
  };

  // Phone number validation
  const validatePhoneNumber = (phone: string): boolean => {
    // Basic phone number validation - accepts various formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  };

  const formatPhoneNumber = (phone: string): string => {
    // Clean and format phone number for E.164 format
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    if (!cleanPhone.startsWith('+')) {
      return cleanPhone.startsWith('1') ? `+${cleanPhone}` : `+1${cleanPhone}`;
    }
    return cleanPhone;
  };

  const startPhoneCall = async () => {
    if (!phoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      toast.error("Please enter a valid phone number");
      return;
    }

    setIsPhoneCallLoading(true);

    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      // Trigger the 8-minute sync window
      window.dispatchEvent(new CustomEvent('voiceSessionStart'));

      toast.info("Calling your phone...");

      // Convert messages to the same format used in browser calls
      const convertedMessages = (formattedMessages || []).map(msg => {
        // Convert role names to OpenAI format
        let role: 'system' | 'user' | 'assistant' = 'system';
        if (msg.role === 'user') role = 'user';
        else if (msg.role === 'model' || msg.role === 'assistant') role = 'assistant';
        else role = 'system'; // For tool_code, tool_response, or any other roles

        return { role, content: msg.content };
      });

      const response = await fetch('/api/outbound-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formattedPhone,
          userId: userId,
          threadId: threadId,
          assistantType: 'voice-assistant',
          profile: profile.profile, // Send profile data
          conversationHistory: convertedMessages // Send conversation context
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Voice assistant is calling ${formattedPhone}`);
        onClose(); // Close modal after successful call initiation

        // Reset form
        setPhoneNumber('');
      } else {
        throw new Error(result.error || 'Failed to initiate call');
      }

    } catch (error) {
      console.error("Failed to start phone call:", error);
      toast.error("Failed to initiate call. Please try again.");
    } finally {
      setIsPhoneCallLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Voice Assistant</DialogTitle>
          <DialogDescription>
            Choose how you'd like to connect with the voice assistant.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Browser Voice Session Section */}
          <div className="space-y-3">
            <h4 className="font-medium">Browser Voice Session</h4>
            <p className="text-sm text-muted-foreground">
              {isSessionActive
                ? "Voice session is active. Click 'Stop Session' to end."
                : "Start a voice session to talk with the assistant. Please note that you will need to grant microphone access when you start the session."}
            </p>
            <Button
              type="button"
              onClick={isSessionActive ? stopSession : startSession}
              disabled={isPhoneCallLoading}
              className="w-full"
            >
              {isSessionActive ? "Stop Session" : "Start Session"}
            </Button>
          </div>

          <Separator />

          {/* Phone Call Section */}
          <div className="space-y-3">
            <h4 className="font-medium">Receive a Voice Assistant phone call</h4>
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium">Your Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="(*************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                disabled={isSessionActive || isPhoneCallLoading}
              />
              <p className="text-xs text-muted-foreground">
                Enter phone number in any format (e.g., 234567890, (*************, ************)
              </p>
            </div>
            <Button
              type="button"
              onClick={startPhoneCall}
              disabled={isSessionActive || isPhoneCallLoading}
              className="w-full"
            >
              {isPhoneCallLoading ? (
                "Calling..."
              ) : (
                <>
                  <Phone className="mr-2 h-4 w-4" />
                  Call My Phone
                </>
              )}
            </Button>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={isSessionActive || isPhoneCallLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}