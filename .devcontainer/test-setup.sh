#!/bin/bash

# Test script to verify devcontainer setup
echo "🧪 Testing DevContainer Setup..."
echo "================================"

# Test 1: Check Node.js version
echo "📦 Node.js version:"
node --version
echo ""

# Test 2: Check pnpm version
echo "📦 pnpm version:"
pnpm --version
echo ""

# Test 3: Check if environment files are mounted
echo "🔧 Environment files:"
if [ -f ".env" ]; then
    echo "✅ .env file found"
else
    echo "❌ .env file not found"
fi

if [ -f ".env.local" ]; then
    echo "✅ .env.local file found"
else
    echo "❌ .env.local file not found"
fi
echo ""

# Test 4: Check if dependencies can be installed
echo "📦 Testing pnpm install..."
if pnpm install --frozen-lockfile; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
fi
echo ""

# Test 5: Check if Next.js can start (dry run)
echo "🚀 Testing Next.js build..."
if pnpm run build; then
    echo "✅ Next.js build successful"
else
    echo "❌ Next.js build failed"
fi
echo ""

# Test 6: Check VS Code extensions (if running in VS Code)
echo "🔧 VS Code extensions check:"
if command -v code &> /dev/null; then
    echo "✅ VS Code CLI available"
    code --list-extensions | grep -E "(typescript|tailwindcss|prettier|eslint)" || echo "⚠️  Some extensions may not be installed"
else
    echo "ℹ️  VS Code CLI not available (normal in container)"
fi
echo ""

# Test 7: Check network connectivity
echo "🌐 Network connectivity:"
if curl -s https://registry.npmjs.org/ > /dev/null; then
    echo "✅ NPM registry accessible"
else
    echo "❌ NPM registry not accessible"
fi

if curl -s https://api.github.com > /dev/null; then
    echo "✅ GitHub API accessible"
else
    echo "❌ GitHub API not accessible"
fi
echo ""

echo "🎉 DevContainer setup test completed!"
echo "================================"
