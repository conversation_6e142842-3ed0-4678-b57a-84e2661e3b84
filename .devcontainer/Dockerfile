FROM --platform=linux/amd64 node:20-bullseye

# Install pnpm
RUN npm install -g pnpm

# Install common development tools
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    vim \
    sudo \
    tmux \
    && rm -rf /var/lib/apt/lists/*

# Install zellij
RUN curl -L https://github.com/zellij-org/zellij/releases/latest/download/zellij-x86_64-unknown-linux-musl.tar.gz | tar -xz -C /usr/local/bin

# Create a non-root user
RUN useradd -m -s /bin/bash vscode && \
    usermod -aG sudo vscode && \
    echo "vscode ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set up workspace
WORKDIR /workspace
RUN chown -R vscode:vscode /workspace

# Switch to non-root user
USER vscode

# Set up shell
RUN echo 'export PATH="/workspace/node_modules/.bin:$PATH"' >> ~/.bashrc

# Default command
CMD ["bash"]