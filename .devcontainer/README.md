# DevContainer Setup for GenNext Healthcare Assistant

## Overview
This devcontainer is configured for Next.js development with TypeScript, Tailwind CSS, and all necessary tools for the GenNext Healthcare Assistant project.

## What's Included

### Base Environment
- **Node.js 20** (Debian Bullseye base)
- **pnpm** package manager
- **Platform**: linux/amd64 (for compatibility with native modules)

### Development Tools
- Git, curl, wget, vim, sudo, tmux
- Zellij terminal multiplexer
- VS Code extensions for TypeScript, Tailwind CSS, Prettier, ESLint

### Environment Configuration
- Environment files (`.env` and `.env.local`) are automatically mounted
- `NODE_ENV=development` set by default
- Port 3000 forwarded for Next.js dev server
- Claude configuration files mounted for AI development

## Files Structure
```
.devcontainer/
├── devcontainer.json    # Main configuration
├── Dockerfile          # Container definition
├── quick-test.sh       # Quick validation script
├── test-setup.sh       # Full setup test script
└── README.md           # This file
```

## Testing the Setup

### Quick Test
Run a quick validation of the container:
```bash
docker run --platform=linux/amd64 --rm -v "$(pwd)":/workspace -w /workspace gennext-devcontainer ./.devcontainer/quick-test.sh
```

### Full Test
Run a comprehensive test including dependency installation:
```bash
docker run --platform=linux/amd64 --rm -v "$(pwd)":/workspace -w /workspace gennext-devcontainer ./.devcontainer/test-setup.sh
```

## VS Code Integration

### Opening in DevContainer
1. Install the "Dev Containers" extension in VS Code
2. Open the project folder
3. Press `Cmd+Shift+P` (Mac) or `Ctrl+Shift+P` (Windows/Linux)
4. Type "Dev Containers: Reopen in Container"
5. Select the command and wait for the container to build

### Features
- Automatic dependency installation via `pnpm install`
- Format on save with Prettier
- TypeScript support with relative imports
- Tailwind CSS IntelliSense
- ESLint integration

## Environment Variables
The following files are automatically mounted:
- `.env` - Base environment variables
- `.env.local` - Local overrides (gitignored)

## Port Forwarding
- Port 3000: Next.js development server
- Automatic notification when port is forwarded

## Troubleshooting

### Architecture Issues
If you encounter native module compilation errors, ensure you're using the `--platform=linux/amd64` flag when running Docker commands manually.

### Permission Issues
The container runs as the `vscode` user with sudo privileges. All workspace files should be accessible.

### Network Issues
The container has full network access for package installation and API calls. If you encounter connectivity issues, check your Docker network configuration.

## Development Workflow
1. Open project in VS Code DevContainer
2. Dependencies are automatically installed
3. Start development server: `pnpm dev`
4. Access application at `http://localhost:3000`

## Updates
To update the container:
1. Modify `Dockerfile` or `devcontainer.json` as needed
2. Rebuild container: `Dev Containers: Rebuild Container` in VS Code
3. Or manually: `docker build --platform=linux/amd64 -f .devcontainer/Dockerfile -t gennext-devcontainer .`
