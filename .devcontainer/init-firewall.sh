#!/bin/bash

# Initialize firewall rules for secure development container
# This script sets up basic network security for Claude Code usage

set -e

echo "Initializing development container firewall..."

# Allow localhost connections for development server
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Allow outbound connections to necessary services
# - NPM registry
# - GitHub
# - OpenAI API
# - Google APIs
# - Development services

# Allow HTTPS (443) and HTTP (80) outbound
iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 80 -j ACCEPT

# Allow DNS
iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT

# Allow SSH (for git)
iptables -A OUTPUT -p tcp --dport 22 -j ACCEPT

# Allow development server port (3000)
iptables -A INPUT -p tcp --dport 3000 -j ACCEPT

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

echo "Firewall rules initialized successfully"

# Execute the original command
exec "$@"