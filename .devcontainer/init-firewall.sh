#!/bin/bash

# Initialize firewall rules for secure development container
# This script sets up basic network security for Claude Code usage

echo "Initializing development container..."

# Check if we have the necessary privileges for iptables
if command -v iptables >/dev/null 2>&1 && [ "$EUID" -eq 0 ]; then
    echo "Setting up firewall rules..."

    # Allow localhost connections for development server
    iptables -A INPUT -i lo -j ACCEPT 2>/dev/null || echo "Warning: Could not set localhost input rules"
    iptables -A OUTPUT -o lo -j ACCEPT 2>/dev/null || echo "Warning: Could not set localhost output rules"

    # Allow established connections
    iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT 2>/dev/null || echo "Warning: Could not set established connection rules"

    # Allow outbound connections to necessary services
    # - NPM registry
    # - GitHub
    # - OpenAI API
    # - Google APIs
    # - Development services

    # Allow HTTPS (443) and HTTP (80) outbound
    iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT 2>/dev/null || echo "Warning: Could not set HTTPS rules"
    iptables -A OUTPUT -p tcp --dport 80 -j ACCEPT 2>/dev/null || echo "Warning: Could not set HTTP rules"

    # Allow DNS
    iptables -A OUTPUT -p udp --dport 53 -j ACCEPT 2>/dev/null || echo "Warning: Could not set DNS UDP rules"
    iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT 2>/dev/null || echo "Warning: Could not set DNS TCP rules"

    # Allow SSH (for git)
    iptables -A OUTPUT -p tcp --dport 22 -j ACCEPT 2>/dev/null || echo "Warning: Could not set SSH rules"

    # Allow development server port (3000)
    iptables -A INPUT -p tcp --dport 3000 -j ACCEPT 2>/dev/null || echo "Warning: Could not set dev server rules"

    # Default policies
    iptables -P INPUT DROP 2>/dev/null || echo "Warning: Could not set default input policy"
    iptables -P FORWARD DROP 2>/dev/null || echo "Warning: Could not set default forward policy"
    iptables -P OUTPUT ACCEPT 2>/dev/null || echo "Warning: Could not set default output policy"

    echo "Firewall rules initialized successfully"
else
    echo "Skipping firewall setup (no privileges or iptables not available)"
fi

# Execute the original command
exec "$@"