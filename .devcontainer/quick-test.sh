#!/bin/bash

# Quick test script to verify devcontainer setup
echo "🧪 Quick DevContainer Test..."
echo "============================="

# Test 1: Check Node.js version
echo "📦 Node.js version:"
node --version
echo ""

# Test 2: Check pnpm version
echo "📦 pnpm version:"
pnpm --version
echo ""

# Test 3: Check if environment files are mounted
echo "🔧 Environment files:"
if [ -f ".env" ]; then
    echo "✅ .env file found"
else
    echo "❌ .env file not found"
fi

if [ -f ".env.local" ]; then
    echo "✅ .env.local file found"
else
    echo "❌ .env.local file not found"
fi
echo ""

# Test 4: Check workspace permissions
echo "🔧 Workspace permissions:"
if [ -w "/workspace" ]; then
    echo "✅ Workspace is writable"
else
    echo "❌ Workspace is not writable"
fi
echo ""

# Test 5: Check network connectivity
echo "🌐 Network connectivity:"
if curl -s --max-time 5 https://registry.npmjs.org/ > /dev/null; then
    echo "✅ NPM registry accessible"
else
    echo "❌ NPM registry not accessible"
fi

if curl -s --max-time 5 https://api.github.com > /dev/null; then
    echo "✅ GitHub API accessible"
else
    echo "❌ GitHub API not accessible"
fi
echo ""

# Test 6: Check if we can create files
echo "🔧 File creation test:"
if echo "test" > /tmp/test.txt 2>/dev/null; then
    echo "✅ Can create files"
    rm -f /tmp/test.txt
else
    echo "❌ Cannot create files"
fi
echo ""

echo "🎉 Quick DevContainer test completed!"
echo "============================="
