{"name": "GenNext Healthcare Assistant", "dockerFile": "Dockerfile", "context": "..", "workspaceFolder": "/workspace", "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint"], "settings": {"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}}}, "postCreateCommand": "pnpm install", "forwardPorts": [3000], "portsAttributes": {"3000": {"label": "Next.js Dev Server", "onAutoForward": "notify"}}, "mounts": ["source=${localWorkspaceFolder}/.claude,target=/workspace/.claude,type=bind,consistency=cached", "source=${localEnv:HOME}/.claude,target=/home/<USER>/.claude,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.env,target=/workspace/.env,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.env.local,target=/workspace/.env.local,type=bind,consistency=cached"], "remoteEnv": {"NODE_ENV": "development"}}